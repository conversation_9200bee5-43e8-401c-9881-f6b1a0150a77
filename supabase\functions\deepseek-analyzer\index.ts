import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AnalyzerRequest {
  subreddits: string[];       // 要分析的subreddit列表
  target_date: string;        // YYYY-MM-DD format
  task_ids: number[];         // 对应的task ID用于状态更新
  batch_id: string;           // 批次ID
}

interface AnalyzerResponse {
  success: boolean;
  message: string;
  totalIdeasGenerated: number;
  subredditsProcessed: number;
  postsProcessed: number;
  taskResults: Array<{
    taskId: number;
    subreddit: string;
    ideasGenerated: number;
    postsAnalyzed: number;
    postsSkipped: number;
    success: boolean;
    error?: string;
  }>;
}

// Industry mapping - using English names to match database
const INDUSTRY_MAPPING = {
  1: 'SaaS & Cloud Services',

  4: 'Mobile App Development',
  5: 'Web & Frontend Development',
  6: 'No-Code/Low-Code Platforms',

  8: 'AI & Machine Learning',
  9: 'E-commerce & Retail',
  10: 'Health & Fitness Tech',
  11: 'EdTech',
  12: 'FinTech',
  13: 'Startup & Business',
  14: 'Consumer Services & Freelance',
  15: 'Enterprise & B2B Services',
  16: 'Digital Marketing & SEO',
  17: 'Social Media Marketing & Influencers',
  18: 'Media & Content Creation',
  19: 'Photography & Visual Arts',
  20: 'Design & Creative Tools',
  21: 'Travel & Transportation',
  22: 'GreenTech & Sustainability',
  23: 'Logistics & Supply Chain',
  24: 'Gaming & Entertainment',

  26: 'AR/VR & Metaverse',
  27: 'BioTech & MedTech',
  28: 'LegalTech',
  29: 'PropTech',
  30: 'Data Science & Analytics',
  31: 'Blockchain & Cryptocurrency',
  32: 'Stock Investment & Trading',
  33: 'Financial Independence & Personal Finance',
  34: 'Audio & Podcast',
  35: 'AgTech',
  36: 'Pet Care & Community',
  37: 'Family & Parenting',
  38: 'General/Trending Topics',
  39: 'MRR showcase'
};

interface RawRedditPost {
  id: number;
  title: string;
  content: string;
  author: string;
  subreddit: string;
  upvotes: number;
  comments: number;
  permalink: string;
  reddit_id: string;
  industry_id: number;
  created_at: string;
  priority_score?: number;
  processing_status?: string;
  quality_score?: number;
}

interface StartupIdea {
  title: string;
  summary: string;
  industry_id: number;
  upvotes: number;
  comments: number;
  keywords: string[];
  subreddit: string;
  reddit_post_urls: string[];
  existing_solutions: string;
  solution_gaps: string;
  market_size: string;
  target_date: string; // YYYY-MM-DD format
  confidence_score: number;
  quality_score: number;
  innovation_score: number;

  // Enhanced analysis fields
  competitor_analysis?: string;
  feasibility_assessment?: string;
  market_potential_score?: number;
  revenue_model?: string[];
  source_post_ids?: number[];

  // New strict validation fields
  market_saturation_score?: number; // 1-10 scale
  pain_severity_score?: number; // 1-10 scale
  payment_willingness_evidence?: string;
  competitive_moat_strength?: number; // 1-10 scale
  technical_complexity_score?: number; // 1-10 scale
  execution_feasibility_score?: number; // 1-10 scale
  regulatory_risk_score?: number; // 1-10 scale
  market_timing_score?: number; // 1-10 scale
  user_validation_count?: number;
  revenue_timeline_months?: number;
  mvp_development_weeks?: number;
  required_team_size?: number;
  overall_viability_score?: number; // 1-100 scale
}

// 计算帖子优先级分数（多因素评分）
function calculatePriorityScore(post: RawRedditPost): number {
  const upvoteWeight = 0.4;
  const commentWeight = 0.3;
  const timeWeight = 0.2;
  const contentWeight = 0.1;
  
  // 标准化分数 (0-100)
  const upvoteScore = Math.min(post.upvotes, 1000) / 10; // 最高100分
  const commentScore = Math.min(post.comments, 500) / 5; // 最高100分
  
  // 时间分数：越新越高
  const postTime = new Date(post.created_at).getTime();
  const now = new Date().getTime();
  const hoursDiff = (now - postTime) / (1000 * 60 * 60);
  const timeScore = Math.max(0, 100 - hoursDiff); // 1小时内100分，逐渐递减
  
  // 内容质量分数：根据标题和内容长度
  const titleLength = post.title?.length || 0;
  const contentLength = post.content?.length || 0;
  const contentScore = Math.min((titleLength + contentLength / 10) / 5, 100);
  
  const finalScore = 
    upvoteScore * upvoteWeight +
    commentScore * commentWeight +
    timeScore * timeWeight +
    contentScore * contentWeight;
    
  return Math.round(finalScore);
}

// 计算想法质量分数
function calculateIdeaQualityScore(idea: any, sourcePost: RawRedditPost): number {
  const confidenceWeight = 0.4;
  const socialWeight = 0.3;
  const innovationWeight = 0.3;
  
  const confidenceScore = idea.confidence_score || 50;
  
  // 社区热度分数
  const socialScore = Math.min((sourcePost.upvotes + sourcePost.comments * 2) / 10, 100);
  
  // 创新度分数（基于关键词多样性和解决方案复杂度）
  const keywordDiversity = (idea.keywords?.length || 0) * 10;
  const solutionComplexity = (idea.solution_gaps?.length || 0) / 10;
  const innovationScore = Math.min(keywordDiversity + solutionComplexity, 100);
  
  const qualityScore = 
    confidenceScore * confidenceWeight +
    socialScore * socialWeight +
    innovationScore * innovationWeight;
    
  return Math.round(qualityScore);
}

// 去重机制：检查相似想法 (改进版)
function calculateSimilarity(idea1: any, idea2: any): number {
  const title1 = idea1.title?.toLowerCase() || '';
  const title2 = idea2.title?.toLowerCase() || '';

  const keywords1 = new Set(idea1.keywords?.map((k: string) => k.toLowerCase()) || []);
  const keywords2 = new Set(idea2.keywords?.map((k: string) => k.toLowerCase()) || []);

  // 标题相似度 - 过滤掉常见词汇
  const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'for', 'with', 'to', 'of', 'in', 'on', 'at', 'by', 'from', 'as', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'saas', 'platform', 'tool', 'app', 'software', 'service', 'solution']);

  const titleWords1 = new Set(title1.split(/\s+/).filter(word => word.length > 2 && !commonWords.has(word)));
  const titleWords2 = new Set(title2.split(/\s+/).filter(word => word.length > 2 && !commonWords.has(word)));

  if (titleWords1.size === 0 || titleWords2.size === 0) {
    return 0; // 如果没有有意义的词汇，相似度为0
  }

  const titleIntersection = new Set([...titleWords1].filter(x => titleWords2.has(x)));
  const titleSimilarity = titleIntersection.size / Math.max(titleWords1.size, titleWords2.size);

  // 关键词相似度
  const keywordIntersection = new Set([...keywords1].filter(x => keywords2.has(x)));
  const keywordSimilarity = keywords1.size > 0 && keywords2.size > 0
    ? keywordIntersection.size / Math.max(keywords1.size, keywords2.size)
    : 0;

  // 摘要相似度 (新增)
  const summary1 = idea1.summary?.toLowerCase() || '';
  const summary2 = idea2.summary?.toLowerCase() || '';
  const summaryWords1 = new Set(summary1.split(/\s+/).filter(word => word.length > 3 && !commonWords.has(word)));
  const summaryWords2 = new Set(summary2.split(/\s+/).filter(word => word.length > 3 && !commonWords.has(word)));

  const summaryIntersection = new Set([...summaryWords1].filter(x => summaryWords2.has(x)));
  const summarySimilarity = summaryWords1.size > 0 && summaryWords2.size > 0
    ? summaryIntersection.size / Math.max(summaryWords1.size, summaryWords2.size)
    : 0;

  // 综合相似度 - 调整权重，加入摘要相似度
  return (titleSimilarity * 0.5 + keywordSimilarity * 0.3 + summarySimilarity * 0.2);
}

// Gemini Balance API interfaces
interface GeminiBalanceResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface GeminiBalanceRequest {
  model: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  max_tokens?: number;
  temperature?: number;
}

// New model list for Gemini Balance API
const BALANCE_API_MODELS = [
  'gemini-2.5-flash',
  
  
  'gemini-2.5-flash-lite-preview-06-17',
  'gemini-2.0-flash',
  'gemini-2.0-flash-lite'

];

export class GeminiBalanceClient {
  private apiKey: string;
  private baseUrl = 'https://geminibalance4me.zeabur.app/v1/chat/completions';
  private timeout: number;
  private lastUsedModel: string | null = null;

  constructor(apiKey?: string, timeout: number = 45000) {
    this.apiKey = apiKey || Deno.env.get('GEMINI_BALANCE_API') || '';
    this.timeout = timeout;

    if (!this.apiKey) {
      throw new Error('GEMINI_BALANCE_API not configured');
    }

    console.log('GeminiBalanceClient initialized with new API endpoint');
  }

  async generateContent(prompt: string): Promise<string> {
    let lastError: string = '';

    // 轮换所有可用的模型
    for (let attempt = 0; attempt < BALANCE_API_MODELS.length; attempt++) {
      const model = BALANCE_API_MODELS[attempt];
      let timeoutId: number | undefined;

      try {
        console.log(`Trying Gemini Balance model: ${model} (attempt ${attempt + 1}/${BALANCE_API_MODELS.length})`);

        const requestBody: GeminiBalanceRequest = {
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are a tech startup analyst expert specializing in identifying technology-based business opportunities from Reddit discussions. Analyze posts and generate ONLY tech startup ideas including SaaS applications, consumer apps, developer tools, API services, and automation platforms. Focus exclusively on software solutions that can solve real user pain points and scale efficiently. Avoid offline businesses, consulting services, or non-tech solutions. IMPORTANT: Return ONLY valid JSON without any markdown formatting, code blocks, or extra text.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 4000,
          temperature: 0.7
        };

        const controller = new AbortController();
        timeoutId = setTimeout(() => controller.abort(), this.timeout) as any;

        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        });

        if (timeoutId) clearTimeout(timeoutId);

        if (response.ok) {
          const data: GeminiBalanceResponse = await response.json();

          if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error(`Invalid response format from Gemini Balance API (${model})`);
          }

          const content = data.choices[0].message.content;
          if (!content) {
            throw new Error(`No content in response from Gemini Balance API (${model})`);
          }

          this.lastUsedModel = model;
          console.log(`✅ Successfully used Gemini Balance model: ${model}`);

          return content;
        } else {
          const errorText = await response.text();
          lastError = `Gemini Balance API error with ${model}: ${response.status} - ${errorText}`;
          console.error(lastError);

          // 如果是rate limit错误，等待一段时间后继续尝试下一个模型
          if (response.status === 429) {
            console.log(`Rate limit hit for ${model}, trying next model...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      } catch (error) {
        if (timeoutId) clearTimeout(timeoutId);
        lastError = `Error calling Gemini Balance API with ${model}: ${error.message}`;
        console.error(lastError);
      }
    }

    // 如果所有模型都失败了，抛出最后一个错误
    throw new Error(`All Gemini Balance models failed. Last error: ${lastError}`);
  }
}

const geminiBalanceClient = new GeminiBalanceClient();

async function callGeminiBalanceAPI(prompt: string): Promise<string> {
  return await geminiBalanceClient.generateContent(prompt);
}

function createAnalysisPrompt(industry: string, posts: RawRedditPost[], targetDate: string): string {
  const postsData = posts.map(post => ({
    id: post.id,
    title: post.title,
    content: post.content ? post.content.substring(0, 3000) : '',
    upvotes: post.upvotes,
    comments: post.comments,
    subreddit: post.subreddit,
    permalink: post.permalink,
    priority_score: post.priority_score
  }));

  // Calculate 5-day range for context
  const targetDateObj = new Date(targetDate);
  const startDate = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() - 4);
  const startDateStr = startDate.toISOString().split('T')[0];
  const dateContext = `from ${startDateStr} to ${targetDate} (5-day range)`;

  // Special prompt for MRR showcase industry
  if (industry === 'MRR showcase') {
    return `
You are a business analyst specializing in analyzing VALIDATED BUSINESS MODELS and PROVEN REVENUE STREAMS from Reddit discussions. Your mission is to identify and summarize successful businesses that are already generating Monthly Recurring Revenue (MRR) or have achieved market validation. Focus on WHAT THEY BUILT and HOW THEY SUCCEEDED rather than identifying new opportunities.

---

### 🎯 MRR SHOWCASE ANALYSIS CRITERIA:

**VALIDATION INDICATORS:**
- Businesses sharing actual revenue numbers (MRR, ARR, monthly income)
- Entrepreneurs showing income reports or earnings milestones
- Successful side projects with paying customers
- Bootstrapped businesses with sustainable revenue
- SaaS products with subscription revenue

**FOCUS AREAS:**
- What specific product/service they built
- What problem it solves and for whom
- How they acquired their first customers
- Revenue model and pricing strategy
- Key success factors and lessons learned
- Business website/URL if mentioned in comments

**EVIDENCE OF SUCCESS:**
- Specific revenue numbers mentioned
- Customer testimonials or feedback
- Growth metrics and milestones
- Sustainable business operations
- Market traction indicators

---

### 📊 ANALYSIS OUTPUT FORMAT:

For each validated business found, provide:

{
  "ideas": [
    {
      "title": "Business Name/Description",
      "summary": "Detailed summary of what they built, the problem solved, target market, and key success factors",
      "business_url": "Website URL if mentioned",
      "revenue_details": "Specific MRR/revenue numbers mentioned",
      "customer_acquisition": "How they got their first customers",
      "lessons_learned": "Key insights and success factors",
      "industry_id": 39,
      "confidence_score": 95,
      "innovation_score": 70,
      "market_potential_score": 85,
      "overall_viability_score": 90,
      "source_post_ids": [post_ids_that_mention_this_business]
    }
  ],
  "analysis_notes": "Summary of validated businesses found and key patterns"
}

**IMPORTANT**: Only include businesses with clear evidence of revenue generation or market validation. If no validated businesses are found in the posts, return empty ideas array.

Posts to analyze ${dateContext}:
${JSON.stringify(postsData, null, 2)}

Focus on extracting proven business models and success stories. Return JSON only, no other text.
`;
  }

  // Default prompt for other industries
  return `
You are an elite startup analyst specializing in identifying HIGHLY PROFITABLE, LOW-COMPETITION technology opportunities from Reddit discussions. Your mission is to find only the most promising software business ideas that meet STRICT commercial viability criteria. Analyze the following Reddit posts from the ${industry} industry ${dateContext} and generate 0-5 exceptional startup ideas. QUALITY OVER QUANTITY - it's better to return zero ideas than mediocre ones.

---

### 🎯 STRICT QUALIFICATION CRITERIA (ALL MUST BE MET):

**MARKET OPPORTUNITY:**
- Market saturation score ≤ 6/10 (avoid crowded markets)
- Pain severity score ≥ 7/10 (significant user frustration)
- At least 3 different users expressing the same pain point
- Clear evidence of payment willingness (users mention paying for solutions)

**COMPETITIVE ADVANTAGE:**
- Competitive moat strength ≥ 6/10 (sustainable differentiation)
- Technical complexity score ≤ 7/10 (achievable by small teams)
- Execution feasibility score ≥ 7/10 (realistic to build and scale)

**BUSINESS VIABILITY:**
- Revenue timeline ≤ 12 months to first paying customers
- MVP development ≤ 16 weeks for small team
- Required team size ≤ 4 people maximum
- Regulatory risk score ≤ 4/10 (minimal compliance barriers)

### ✅ TARGET SOLUTION TYPES (TECH-ONLY):
1. **B2B SaaS Platforms** – subscription software solving business problems
2. **Developer Tools & APIs** – productivity tools for technical teams
3. **Automation & Workflow Tools** – eliminating manual processes
4. **Niche Vertical Software** – specialized tools for specific industries
5. **Integration & Connector Tools** – bridging gaps between existing systems

### ❌ AUTOMATIC REJECTION CRITERIA:
- Physical products or offline services
- Consulting, coaching, or education services
- Content creation or media businesses
- Highly regulated industries (finance, healthcare, legal)
- Solutions requiring large teams (>4 people)
- Markets with dominant incumbents (Google, Microsoft, etc.)

---

### � STRICT PAIN POINT VALIDATION:
Look for MULTIPLE STRONG SIGNALS indicating genuine market demand:

**HIGH-VALUE PAIN INDICATORS:**

- Users explicitly mentioning willingness to pay for solutions
- Current workarounds costing significant time/money
- Frequent, recurring problems (daily/weekly occurrence)
- Users building their own tools/scripts as workarounds
- Complaints about expensive existing solutions
- Integration gaps between popular tools

**PAYMENT WILLINGNESS EVIDENCE (REQUIRED):**
- "I would pay $X for something that does Y"
- "Currently paying $X for Z but it doesn't do Y"
- "Spending X hours/week on this manual process"
- "Looking for alternatives to expensive tool X"
- Users sharing paid tools they've tried and found lacking

**SELF-BUILT SOLUTION VALIDATION (GOLD STANDARD):**
- "Built a script/tool to solve X because nothing existed"
- "Created internal tool that team loves, considering commercializing"
- "Made a prototype that friends want to use"
- "Automated X process with custom solution"
- DIY solutions getting positive community feedback

**MARKET TIMING INDICATORS:**
- Recent changes making existing solutions obsolete
- New regulations creating compliance needs
- Technology shifts opening new opportunities
- Growing user bases in underserved niches

---

### 💰 VALID MONETIZATION MODELS (required for each idea):
- Subscription (SaaS)
- Freemium with premium features
- API usage-based pricing
- One-time software purchase/license
- Marketplace transaction fees
- Enterprise contract or licensing

---

Reddit Discussion Data (${dateContext}, sorted by priority):
${JSON.stringify(postsData, null, 2)}

### 📊 STRICT OUTPUT FORMAT (JSON ONLY, no markdown):
{
  "ideas": [
    {
      "title": "Compelling, specific startup name",
      "summary": "2-3 sentences: problem + target users + solution approach",
      "keywords": ["specific", "relevant", "tech", "keywords"],
      "existing_solutions": "Current alternatives and their specific shortcomings",
      "solution_gaps": "Exact gaps this solution fills",
      "competitor_analysis": "Top 3 competitors, their weaknesses, differentiation strategy",
      "feasibility_assessment": "Technical complexity, resource requirements, risks",
      "market_size": "Specific target market size with numbers/estimates",
      "revenue_model": ["subscription", "usage-based", "marketplace"],

      // STRICT VALIDATION SCORES (1-10 scale except where noted)
      "market_saturation_score": 4,
      "pain_severity_score": 8,
      "payment_willingness_evidence": "Specific quotes/evidence of users willing to pay",
      "competitive_moat_strength": 7,
      "technical_complexity_score": 5,
      "execution_feasibility_score": 8,
      "regulatory_risk_score": 2,
      "market_timing_score": 9,
      "user_validation_count": 4,
      "revenue_timeline_months": 8,
      "mvp_development_weeks": 12,
      "required_team_size": 3,

      // OVERALL SCORES
      "confidence_score": 85,
      "innovation_score": 75,
      "market_potential_score": 80,
      "overall_viability_score": 82,
      "source_post_ids": [123, 456, 789]
    }
  ],
  "analysis_notes": "Explain selection criteria applied and why ideas qualify or why none were found"
}

### 🎯 STRICT ANALYSIS REQUIREMENTS:

**MANDATORY QUALIFICATION CHECKS:**

2. **Payment Evidence**: Clear willingness to pay or current spending on inadequate solutions
3. **Competition Analysis**: Market saturation score ≤ 6/10
4. **Technical Feasibility**: Complexity score ≤ 7/10, achievable by small team
5. **Business Viability**: Revenue timeline ≤ 12 months, MVP ≤ 16 weeks
6. **Confidence Threshold**: Only suggest ideas with confidence ≥ 80/100

**SCORING REQUIREMENTS (1-10 scale unless noted):**
- Market saturation: 1=blue ocean, 10=completely saturated
- Pain severity: 1=minor annoyance, 10=critical business problem
- Competitive moat: 1=easily copied, 10=strong defensibility
- Technical complexity: 1=simple, 10=requires large engineering team
- Execution feasibility: 1=very difficult, 10=straightforward execution
- Regulatory risk: 1=no compliance issues, 10=heavily regulated
- Market timing: 1=too early/late, 10=perfect timing

**AUTOMATIC REJECTION CRITERIA:**
- Market saturation score > 6
- Pain severity score < 7
- Technical complexity score > 7
- Execution feasibility score < 7
- Required team size > 4 people
- Revenue timeline > 12 months
- High regulatory risk (score > 4)
- No clear payment willingness evidence


**PRIORITIZATION FACTORS:**
- Self-built solutions = highest priority (proven demand)
- Multiple payment willingness mentions
- Integration gaps between popular tools
- Automation of expensive manual processes
- Underserved niches with growing user bases

**CRITICAL**: Each idea MUST include specific "source_post_ids" that directly support the pain point and market opportunity. If posts don't meet strict criteria, return empty ideas array.

Focus on ${dateContext} trends. Return JSON only, no other text.
`;
}

// 这些函数已经不再需要，因为我们现在使用具体的目标日期而不是时间范围

// Update task status and statistics
async function updateTaskStatus(
  supabaseClient: any,
  taskId: number,
  status: string,
  updates: { ideas_generated?: number; error_message?: string; posts_processed?: number } = {}
): Promise<void> {
  const updateData: any = { status };
  
  if (status === 'complete_analysis') {
    updateData.completed_at = new Date().toISOString();
  }
  
  Object.assign(updateData, updates);
  
  const { error } = await supabaseClient
    .from('scrape_tasks')
    .update(updateData)
    .eq('id', taskId);
  
  if (error) {
    console.error(`Error updating task ${taskId}:`, error);
  }
}

// 管理每个 subreddit 的最高质量想法（最多保留3个）
async function manageTopIdeasForSubreddit(
  supabaseClient: any,
  industryId: number,
  newIdeas: StartupIdea[],
  targetDate: string,
  subreddit: string
): Promise<number> {
  let savedCount = 0;

  // 定义扩展的想法类型
  type ExtendedIdea = StartupIdea & { id?: number };

  // 去重：移除新想法中相似的想法
  const uniqueNewIdeas: ExtendedIdea[] = [];
  for (const idea of newIdeas) {
    let isDuplicate = false;
    for (const existingIdea of uniqueNewIdeas) {
      const similarity = calculateSimilarity(idea, existingIdea);
      if (similarity > 0.7) { // 70%相似度阈值
        console.log(`🔄 Filtering duplicate idea within batch: "${idea.title}" (similarity: ${(similarity * 100).toFixed(1)}%)`);
        // 保留质量分数更高的
        if (idea.quality_score > existingIdea.quality_score) {
          const index = uniqueNewIdeas.indexOf(existingIdea);
          uniqueNewIdeas[index] = idea;
          console.log(`✅ Replaced with higher quality idea: ${idea.quality_score} > ${existingIdea.quality_score}`);
        }
        isDuplicate = true;
        break;
      }
    }
    if (!isDuplicate) {
      uniqueNewIdeas.push(idea);
    }
  }

  // 检查与现有想法的相似性，避免重复（按 subreddit 查询）
  const { data: existingIdeas, error: fetchError } = await supabaseClient
    .from('startup_ideas')
    .select('*')
    .eq('subreddit', subreddit)
    .eq('target_date', targetDate)
    .order('quality_score', { ascending: false });

  if (fetchError) {
    console.error(`Error fetching existing ideas for subreddit ${subreddit} on ${targetDate}:`, fetchError);
  }

  const finalNewIdeas: ExtendedIdea[] = [];
  for (const newIdea of uniqueNewIdeas) {
    let isDuplicate = false;
    for (const existingIdea of (existingIdeas || [])) {
      const similarity = calculateSimilarity(newIdea, existingIdea);
      if (similarity > 0.7) { // 70%相似度阈值
        console.log(`🔄 Filtering duplicate idea against existing: "${newIdea.title}" vs "${existingIdea.title}" (similarity: ${(similarity * 100).toFixed(1)}%)`);
        isDuplicate = true;
        break;
      }
    }
    if (!isDuplicate) {
      finalNewIdeas.push(newIdea);
    }
  }

  // 按质量分数排序，保留前3个新想法
  finalNewIdeas.sort((a, b) => b.quality_score - a.quality_score);
  const topNewIdeas = finalNewIdeas.slice(0, 3);

  // 插入新的想法
  for (const idea of topNewIdeas) {
    const { error: insertError } = await supabaseClient
      .from('startup_ideas')
      .insert(idea);

    if (insertError) {
      console.error(`❌ Error inserting idea "${idea.title}" for industry ${industryId} on ${targetDate}:`, insertError);
    } else {
      savedCount++;
    }
  }

  // 检查该 subreddit 在该日期是否有超过3个想法，如果有则删除质量较低的
  const { data: allIdeasForDate, error: countError } = await supabaseClient
    .from('startup_ideas')
    .select('*')
    .eq('subreddit', subreddit)
    .eq('target_date', targetDate)
    .order('quality_score', { ascending: false });

  if (!countError && allIdeasForDate && allIdeasForDate.length > 3) {
    const idsToDelete = allIdeasForDate.slice(3).map((idea: any) => idea.id);
    const { error: deleteError } = await supabaseClient
      .from('startup_ideas')
      .delete()
      .in('id', idsToDelete);

    if (deleteError) {
      console.error(`Error deleting excess ideas for subreddit ${subreddit} on ${targetDate}:`, deleteError);
    } else {
      console.log(`🗑️ Deleted ${idsToDelete.length} excess ideas for subreddit ${subreddit} on ${targetDate}`);
    }
  }

  // 合并所有统计信息到一个日志
  console.log(`� Industry ${industryId} (${targetDate}): ${newIdeas.length} generated → ${uniqueNewIdeas.length} after batch dedup → ${finalNewIdeas.length} after DB dedup → ${savedCount} saved (${((savedCount / Math.max(newIdeas.length, 1)) * 100).toFixed(1)}% success rate)`);

  return savedCount;
}

// Process subreddit analysis directly from database (no mapping needed)
async function analyzeSubredditDirect(
  supabaseClient: any,
  subreddit: string,
  targetDate: string
): Promise<{ ideasGenerated: number; postsAnalyzed: number; postsSkipped: number; error?: string }> {
  try {
    // 计算5天日期范围，与 analyzeIndustry 函数保持一致
    const targetDateObj = new Date(targetDate + 'T00:00:00.000Z');
    // Start from 5 days ago (targetDate - 4 days) at 00:00:00
    const targetDateStart = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() - 4, 0, 0, 0);
    // End at target date at 23:59:59
    const targetDateEnd = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate(), 23, 59, 59);

    console.log(`📅 Querying r/${subreddit} for date range: ${targetDateStart.toISOString().split('T')[0]} to ${targetDateEnd.toISOString().split('T')[0]}`);

    // 直接查询该 subreddit 的帖子，使用 created_at 字段而不是不存在的 target_date 字段
    const { data: posts, error } = await supabaseClient
      .from('raw_reddit_posts')
      .select('*')
      .eq('subreddit', subreddit)
      .gte('created_at', targetDateStart.toISOString())
      .lte('created_at', targetDateEnd.toISOString())
      .order('priority_score', { ascending: false });

    if (error) {
      console.error(`Error fetching posts for r/${subreddit}:`, error);
      // Database errors are generally not retryable
      throw new NonRetryableError(`Database error: ${error.message}`);
    }

    if (!posts || posts.length === 0) {
      console.log(`📭 No posts found for r/${subreddit} in date range ${targetDateStart.toISOString().split('T')[0]} to ${targetDateEnd.toISOString().split('T')[0]}`);
      // No posts is not an error that should be retried
      throw new NonRetryableError('No unprocessed posts available for analysis');
    }

    // 从第一个帖子获取行业信息
    const industryId = posts[0].industry_id;
    const industryName = INDUSTRY_MAPPING[industryId] || `Industry ${industryId}`;

    console.log(`� r/${subreddit} → ${industryName} (ID: ${industryId}): ${posts.length} posts found in date range`);

    // 调用原有的分析逻辑
    return await analyzeIndustry(supabaseClient, industryId, industryName, targetDate, subreddit);

  } catch (error) {
    // If it's already a RetryableError or NonRetryableError, re-throw it
    if (error instanceof RetryableError || error instanceof NonRetryableError) {
      throw error;
    }

    console.error(`Error analyzing r/${subreddit}:`, error);
    // Unknown errors are treated as retryable
    throw new RetryableError(`Unknown error analyzing r/${subreddit}: ${error.message}`, error);
  }
}

// Custom error types for retry logic
class RetryableError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'RetryableError';
  }
}

class NonRetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NonRetryableError';
  }
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 2000, // 2 seconds
  maxDelay: 16000  // 16 seconds
};

// Calculate retry delay with exponential backoff
function calculateRetryDelay(attempt: number): number {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(2, attempt - 1);
  return Math.min(delay, RETRY_CONFIG.maxDelay);
}

// Process subreddit analysis with retry logic
async function analyzeSubredditWithRetry(
  supabaseClient: any,
  originalSubreddit: string,
  targetDate: string,
  taskId: number
): Promise<{ ideasGenerated: number; postsAnalyzed: number; postsSkipped: number; error?: string }> {
  // Normalize subreddit name to lowercase to ensure consistency
  const subreddit = originalSubreddit.toLowerCase();

  if (originalSubreddit !== subreddit) {
    console.log(`🔄 Normalized subreddit name: "${originalSubreddit}" -> "${subreddit}"`);
  }

  console.log(`\n🎯 Starting r/${subreddit} analysis (${targetDate}) with retry support`);

  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= RETRY_CONFIG.maxRetries + 1; attempt++) {
    try {
      if (attempt > 1) {
        const delay = calculateRetryDelay(attempt - 1);
        console.log(`🔄 Retry attempt ${attempt - 1}/${RETRY_CONFIG.maxRetries} for r/${subreddit} after ${delay}ms delay...`);

        // Update task status to retrying
        await updateTaskStatus(supabaseClient, taskId, 'retrying', {
          error_message: `Retry attempt ${attempt - 1}/${RETRY_CONFIG.maxRetries}: ${lastError?.message}`
        });

        await new Promise(resolve => setTimeout(resolve, delay));
      }

      const result = await analyzeSubredditDirect(supabaseClient, subreddit, targetDate);

      // Success - log and return
      const status = result.error ? `❌ ${result.error}` : '✅ Success';
      const retryInfo = attempt > 1 ? ` (succeeded on attempt ${attempt})` : '';
      console.log(`📊 r/${subreddit}: ${result.ideasGenerated} ideas saved, ${result.postsAnalyzed + result.postsSkipped} posts processed (${result.postsAnalyzed} analyzed, ${result.postsSkipped} skipped) - ${status}${retryInfo}`);

      return result;

    } catch (error) {
      lastError = error;
      console.error(`❌ Attempt ${attempt} failed for r/${subreddit}:`, error.message);

      // Check if this is a retryable error and we have more attempts
      if (error instanceof RetryableError && attempt <= RETRY_CONFIG.maxRetries) {
        console.log(`🔄 Will retry r/${subreddit} (retryable error: ${error.message})`);
        continue;
      }

      // Non-retryable error or max retries reached
      if (error instanceof NonRetryableError) {
        console.log(`🚫 Non-retryable error for r/${subreddit}: ${error.message}`);
      } else if (attempt > RETRY_CONFIG.maxRetries) {
        console.log(`🚫 Max retries (${RETRY_CONFIG.maxRetries}) reached for r/${subreddit}`);
      }

      // Return error result
      return {
        ideasGenerated: 0,
        postsAnalyzed: 0,
        postsSkipped: 0,
        error: error.message
      };
    }
  }

  // This should never be reached, but just in case
  return {
    ideasGenerated: 0,
    postsAnalyzed: 0,
    postsSkipped: 0,
    error: lastError?.message || 'Unknown error'
  };
}

// Process subreddit analysis with improved logic (now throws RetryableError/NonRetryableError)
async function analyzeSubreddit(
  supabaseClient: any,
  originalSubreddit: string,
  targetDate: string
): Promise<{ ideasGenerated: number; postsAnalyzed: number; postsSkipped: number; error?: string }> {
  // This function is now just a wrapper that calls analyzeSubredditDirect
  // The retry logic is handled by analyzeSubredditWithRetry
  const subreddit = originalSubreddit.toLowerCase();
  return await analyzeSubredditDirect(supabaseClient, subreddit, targetDate);
}

// Helper function to map subreddit to industry ID
// 不再需要映射函数 - 直接从数据库获取行业信息

// Process industry analysis with improved logic (modified to support subreddit filtering)
async function analyzeIndustry(
  supabaseClient: any,
  industryId: number,
  industryName: string,
  targetDate: string,
  specificSubreddit?: string
): Promise<{ ideasGenerated: number; postsAnalyzed: number; postsSkipped: number; error?: string }> {
  try {
    console.log(`🔍 Analyzing ${industryName} (ID: ${industryId}) industry for 5-day range ending ${targetDate}...`);

    // 获取未处理的帖子，按5天日期范围过滤
    const targetDateObj = new Date(targetDate + 'T00:00:00.000Z');
    // Start from 5 days ago (targetDate - 4 days) at 00:00:00
    const targetDateStart = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate() - 4, 0, 0, 0);
    // End at target date at 23:59:59
    const targetDateEnd = new Date(targetDateObj.getFullYear(), targetDateObj.getMonth(), targetDateObj.getDate(), 23, 59, 59);
    
    const startDateStr = targetDateStart.toISOString().split('T')[0];
    const endDateStr = targetDateEnd.toISOString().split('T')[0];
    console.log(`📅 Target 5-day range for ${industryName}: ${startDateStr} to ${endDateStr}`);

    let query = supabaseClient
      .from('raw_reddit_posts')
      .select('*')
      .eq('industry_id', industryId)
      .eq('analyzed', false)  // 只处理未分析过的posts
      .gte('created_at', targetDateStart.toISOString())
      .lte('created_at', targetDateEnd.toISOString());

    // If analyzing a specific subreddit, filter by subreddit (case-insensitive)
    if (specificSubreddit) {
      query = query.ilike('subreddit', specificSubreddit);
      console.log(`🎯 Filtering posts for specific subreddit: r/${specificSubreddit} (case-insensitive)`);
    }

    const { data: allPosts, error: fetchError } = await query
      .order('priority_score', { ascending: false })
      .order('upvotes', { ascending: false })
      .order('comments', { ascending: false })
      .order('created_at', { ascending: false });

    if (fetchError) {
      throw new Error(`Database error: ${fetchError.message}`);
    }

    console.log(`📊 Found ${allPosts?.length || 0} unprocessed posts for ${industryName} on ${targetDate}`);

    if (!allPosts || allPosts.length === 0) {
      console.log(`⚠️ No unprocessed posts found for ${industryName}`);
      return { ideasGenerated: 0, postsAnalyzed: 0, postsSkipped: 0, error: 'No unprocessed posts available for analysis' };
    }

    // 为所有帖子计算优先级分数（如果还没有或为0）
    const postsWithPriority = allPosts.map(post => {
      if (!post.priority_score || post.priority_score === 0) {
        post.priority_score = calculatePriorityScore(post);
      }
      return post;
    });

    // 更新所有帖子的优先级分数
    for (const post of postsWithPriority) {
      const originalPost = allPosts.find(p => p.id === post.id);
      const originalScore = originalPost?.priority_score;

      // 如果原始分数是 null/undefined 或者分数发生了变化，则更新
      if (originalScore === null || originalScore === undefined || originalScore !== post.priority_score) {
        console.log(`📊 Updating priority score for post ${post.id}: ${originalScore} -> ${post.priority_score}`);

        const { error: updateError } = await supabaseClient
          .from('raw_reddit_posts')
          .update({ priority_score: post.priority_score })
          .eq('id', post.id);

        if (updateError) {
          console.error(`❌ Error updating priority score for post ${post.id}:`, updateError);
        } else {
          console.log(`✅ Successfully updated priority score for post ${post.id}`);
        }
      }
    }

    // 按优先级重新排序
    postsWithPriority.sort((a, b) => {
      if (b.priority_score !== a.priority_score) return b.priority_score - a.priority_score;
      if (b.upvotes !== a.upvotes) return b.upvotes - a.upvotes;
      if (b.comments !== a.comments) return b.comments - a.comments;
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });

    // 限制处理200个帖子
    const postsToAnalyze = postsWithPriority.slice(0, 200);
    const postsToSkip = postsWithPriority.slice(200);

    console.log(`📝 Will analyze ${postsToAnalyze.length} posts, skip ${postsToSkip.length} posts for ${industryName}`);

    // 标记跳过的帖子
    if (postsToSkip.length > 0) {
      const skipPostIds = postsToSkip.map(post => post.id);
      const { error: skipError } = await supabaseClient
        .from('raw_reddit_posts')
        .update({ 
          processing_status: 'skipped_low_priority',
          analyzed: true,
          analyzed_at: new Date().toISOString()
        })
        .in('id', skipPostIds);

      if (skipError) {
        console.error(`Error marking posts as skipped for ${industryName}:`, skipError);
      } else {
        console.log(`✅ Marked ${skipPostIds.length} posts as skipped for ${industryName}`);
      }
    }

    if (postsToAnalyze.length === 0) {
      console.log(`⚠️ No posts to analyze for ${industryName}`);
      return { 
        ideasGenerated: 0, 
        postsAnalyzed: 0, 
        postsSkipped: postsToSkip.length, 
        error: 'No posts to analyze' 
      };
    }

    // 分批处理帖子以避免token限制
    const batchSize = 30; // 减少批次大小以提高质量
    const postBatches: RawRedditPost[][] = [];
    for (let i = 0; i < postsToAnalyze.length; i += batchSize) {
      postBatches.push(postsToAnalyze.slice(i, i + batchSize));
    }

    const allNewIdeas: StartupIdea[] = [];
    let postsAnalyzedCount = 0;

    // 分析每批帖子
    for (let i = 0; i < postBatches.length; i++) {
      const batchPosts = postBatches[i];
      
      try {
        console.log(`📝 Analyzing batch ${i + 1}/${postBatches.length} for ${industryName} (${batchPosts.length} posts)...`);
        
        const prompt = createAnalysisPrompt(industryName, batchPosts, targetDate);
        const analysisResponse = await callGeminiBalanceAPI(prompt);
        
        // 解析响应
        let analysisData;
        try {
          let cleanResponse = analysisResponse.trim();

          // 清理markdown代码块
          if (cleanResponse.startsWith('```json')) {
            cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
          } else if (cleanResponse.startsWith('```')) {
            cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
          }

          // 清理JSON字符串中的问题字符
          cleanResponse = cleanResponse.replace(/"\$([0-9.]+[BMK]?)\s+([^"]*?)"/g, '"$$$1 $2"');
          cleanResponse = cleanResponse.replace(/([^\\])\$([0-9.]+[BMK]?)\s+/g, '$1$$$2 ');

          // 修复由于截断导致的不完整JSON
          // 首先检查是否有未闭合的字符串
          const openQuotes = (cleanResponse.match(/"/g) || []).length;
          if (openQuotes % 2 !== 0) {
            // 有未闭合的引号，找到最后一个引号并截断到那里
            const lastQuoteIndex = cleanResponse.lastIndexOf('"');
            if (lastQuoteIndex > 0) {
              // 找到最后一个完整的字段
              const beforeLastQuote = cleanResponse.substring(0, lastQuoteIndex);
              const lastCommaIndex = beforeLastQuote.lastIndexOf(',');
              const lastBraceIndex = beforeLastQuote.lastIndexOf('{');

              if (lastCommaIndex > lastBraceIndex) {
                // 截断到最后一个逗号之前
                cleanResponse = beforeLastQuote.substring(0, lastCommaIndex);
              } else {
                // 截断到最后一个大括号之前
                cleanResponse = beforeLastQuote.substring(0, lastBraceIndex);
              }
            }
          }

          // 确保JSON结构完整
          if (!cleanResponse.endsWith('}') && !cleanResponse.endsWith(']}')) {
            // 检查是否在数组中
            if (cleanResponse.includes('"ideas":[')) {
              // 找到最后一个完整的对象
              const lastBraceIndex = cleanResponse.lastIndexOf('}');
              if (lastBraceIndex > 0) {
                cleanResponse = cleanResponse.substring(0, lastBraceIndex + 1) + ']}';
              } else {
                // 如果没有找到完整的对象，创建一个空的ideas数组
                cleanResponse = '{"ideas":[]}';
              }
            } else {
              // 简单的对象，添加闭合括号
              cleanResponse += '}';
            }
          }

          analysisData = JSON.parse(cleanResponse);
        } catch (parseError) {
          console.error(`Failed to parse Gemini response for ${industryName}:`, parseError);
          console.error('Raw response:', analysisResponse.substring(0, 1000));

          // JSON解析失败直接重试，不尝试修复
          // 这样可以让API重新生成完整的响应，避免丢失数据
          console.log(`🔄 JSON parsing failed for ${industryName}, will retry to get complete response`);
          throw new RetryableError(`JSON parsing failed for ${industryName}: ${parseError.message}`, parseError);
        }

        if (!analysisData.ideas || !Array.isArray(analysisData.ideas)) {
          console.error(`Invalid ideas format for ${industryName}`);
          continue;
        }

        // 检查是否没有生成想法，这是正常情况
        if (analysisData.ideas.length === 0) {
          const analysisNotes = analysisData.analysis_notes || 'No specific reason provided';
          console.log(`📝 ${industryName} - No ideas generated: ${analysisNotes}`);
          // 这不是错误，继续处理下一批
          continue;
        }

        // 处理想法
        for (const ideaData of analysisData.ideas) {
          try {
            // 使用Gemini返回的source_post_ids来找到相关帖子
            let sourcePosts = batchPosts;
            let redditPostUrls: string[] = [];

            if (ideaData.source_post_ids && Array.isArray(ideaData.source_post_ids) && ideaData.source_post_ids.length > 0) {
              // 根据source_post_ids筛选相关帖子
              sourcePosts = batchPosts.filter(post => ideaData.source_post_ids.includes(post.id));

              if (sourcePosts.length === 0) {
                console.log(`⚠️ Warning: No posts found for source_post_ids ${ideaData.source_post_ids.join(', ')} in idea "${ideaData.title}". Using all batch posts as fallback.`);
                sourcePosts = batchPosts;
              } else {
                console.log(`✅ Found ${sourcePosts.length} specific source posts for idea "${ideaData.title}"`);
              }
            } else {
              console.log(`⚠️ Warning: No source_post_ids provided for idea "${ideaData.title}". Using all batch posts as fallback.`);
            }

            if (sourcePosts.length === 0) continue;

            const primaryPost = sourcePosts[0];
            const totalUpvotes = sourcePosts.reduce((sum, post) => sum + post.upvotes, 0);
            const totalComments = sourcePosts.reduce((sum, post) => sum + post.comments, 0);
            redditPostUrls = sourcePosts.map(post => post.permalink);

            // 计算想法质量分数
            const qualityScore = calculateIdeaQualityScore(ideaData, primaryPost);

            const startupIdea: StartupIdea = {
              title: ideaData.title || 'Untitled Idea',
              summary: ideaData.summary || 'No summary provided',
              industry_id: industryId,
              upvotes: totalUpvotes,
              comments: totalComments,
              keywords: Array.isArray(ideaData.keywords) ? ideaData.keywords : [],
              subreddit: primaryPost.subreddit,
              reddit_post_urls: redditPostUrls,
              existing_solutions: ideaData.existing_solutions || '',
              solution_gaps: ideaData.solution_gaps || '',
              market_size: ideaData.market_size || '',
              target_date: targetDate,
              confidence_score: ideaData.confidence_score || 50,
              quality_score: qualityScore,
              innovation_score: ideaData.innovation_score || 50,

              // Enhanced analysis fields
              competitor_analysis: ideaData.competitor_analysis || '',
              feasibility_assessment: ideaData.feasibility_assessment || '',
              market_potential_score: ideaData.market_potential_score || 50,
              revenue_model: Array.isArray(ideaData.revenue_model) ? ideaData.revenue_model : [],
              source_post_ids: Array.isArray(ideaData.source_post_ids) ? ideaData.source_post_ids : [],

              // New strict validation fields
              market_saturation_score: ideaData.market_saturation_score || 0,
              pain_severity_score: ideaData.pain_severity_score || 0,
              payment_willingness_evidence: ideaData.payment_willingness_evidence || '',
              competitive_moat_strength: ideaData.competitive_moat_strength || 0,
              technical_complexity_score: ideaData.technical_complexity_score || 0,
              execution_feasibility_score: ideaData.execution_feasibility_score || 0,
              regulatory_risk_score: ideaData.regulatory_risk_score || 0,
              market_timing_score: ideaData.market_timing_score || 0,
              user_validation_count: ideaData.user_validation_count || 0,
              revenue_timeline_months: ideaData.revenue_timeline_months || 0,
              mvp_development_weeks: ideaData.mvp_development_weeks || 0,
              required_team_size: ideaData.required_team_size || 1,
              overall_viability_score: ideaData.overall_viability_score || 0
            };

            allNewIdeas.push(startupIdea);
          } catch (ideaError) {
            console.error(`❌ Error processing individual idea for ${industryName}:`, ideaError);
          }
        }
        
        // 标记已分析的帖子
        const postIds = batchPosts.map(post => post.id);
        const { error: updateError } = await supabaseClient
          .from('raw_reddit_posts')
          .update({ 
            processing_status: 'analyzed',
            analyzed: true, 
            analyzed_at: new Date().toISOString() 
          })
          .in('id', postIds);
        
        if (updateError) {
          console.error(`Failed to mark posts as analyzed for ${industryName}:`, updateError);
        } else {
          postsAnalyzedCount += postIds.length;
          console.log(`✅ Marked ${postIds.length} posts as analyzed for ${industryName}`);
        }
        
        // API调用之间的延迟
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (apiError) {
        console.error(`Gemini Balance API error for ${industryName} batch ${i + 1}:`, apiError);

        // Check if this is an API failure that should be retried
        if (apiError.message && apiError.message.includes('All Gemini Balance models failed')) {
          // This is a retryable API error
          throw new RetryableError(`API failure for ${industryName}: ${apiError.message}`, apiError);
        }

        // For other API errors, mark posts as failed but don't retry
        const postIds = batchPosts.map(post => post.id);
        await supabaseClient
          .from('raw_reddit_posts')
          .update({
            processing_status: 'skipped_api_error',
            analyzed: true,
            analyzed_at: new Date().toISOString()
          })
          .in('id', postIds);
      }
    }

    // 管理该 subreddit 的顶级想法（最多保留3个最高质量的）
    const savedIdeasCount = await manageTopIdeasForSubreddit(supabaseClient, industryId, allNewIdeas, targetDate, specificSubreddit || 'unknown');

    return {
      ideasGenerated: savedIdeasCount,
      postsAnalyzed: postsAnalyzedCount,
      postsSkipped: postsToSkip.length
    };

  } catch (error) {
    console.error(`Error analyzing ${industryName}:`, error);
    return { 
      ideasGenerated: 0, 
      postsAnalyzed: 0, 
      postsSkipped: 0, 
      error: error.message 
    };
  }
}

// Main serve function
Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const {
      subreddits,
      target_date,
      task_ids,
      batch_id
    }: AnalyzerRequest = await req.json();

    // Validate required parameters
    if (!subreddits || !Array.isArray(subreddits) || subreddits.length === 0) {
      throw new Error('subreddits is required and must be a non-empty array');
    }
    if (!task_ids || !Array.isArray(task_ids) || task_ids.length === 0) {
      throw new Error('task_ids is required and must be a non-empty array');
    }
    if (subreddits.length !== task_ids.length) {
      throw new Error('subreddits and task_ids arrays must have the same length');
    }
    if (!target_date) {
      throw new Error('target_date is required');
    }
    if (!batch_id) {
      throw new Error('batch_id is required');
    }

    console.log(`🧠 Starting enhanced Gemini Balance analysis for subreddits: ${subreddits.join(', ')}, target date: ${target_date}, batch: ${batch_id}`);
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    let totalIdeasGenerated = 0;
    let totalPostsProcessed = 0;
    const taskResults: Array<{
      taskId: number;
      subreddit: string;
      ideasGenerated: number;
      postsAnalyzed: number;
      postsSkipped: number;
      success: boolean;
      error?: string;
    }> = [];

    // Process subreddit tasks concurrently
    console.log(`🚀 Starting concurrent processing of ${subreddits.length} subreddits...`);

    const subredditTasks = subreddits.map(async (subreddit, i) => {
      const taskId = task_ids[i];

      try {
        console.log(`📱 Processing subreddit r/${subreddit} for task ${taskId}...`);

        const { ideasGenerated, postsAnalyzed, postsSkipped, error } = await analyzeSubredditWithRetry(
          supabaseClient,
          subreddit,
          target_date,
          taskId
        );
        
        const postsProcessed = postsAnalyzed + postsSkipped;
        
        if (error && ideasGenerated === 0) {
          // Check if this is a "no posts" error which should not be retried
          const isNoPostsError = error.includes('No unprocessed posts available for analysis');
          
          // Update task status to failed
          await updateTaskStatus(supabaseClient, taskId, isNoPostsError ? 'complete_analysis' : 'failed', {
            error_message: isNoPostsError ? undefined : error,
            posts_processed: postsProcessed,
            ideas_generated: 0
          });
          
          return {
            taskId,
            subreddit,
            ideasGenerated: 0,
            postsAnalyzed,
            postsSkipped,
            postsProcessed,
            success: isNoPostsError, // Consider "no posts" as success to avoid retries
            error: isNoPostsError ? undefined : error
          };
        } else {
          // Update task status to complete_analysis
          await updateTaskStatus(supabaseClient, taskId, 'complete_analysis', {
            ideas_generated: ideasGenerated,
            posts_processed: postsProcessed
          });

          return {
            taskId,
            subreddit,
            ideasGenerated,
            postsAnalyzed,
            postsSkipped,
            postsProcessed,
            success: true
          };
        }
        
      } catch (error) {
        console.error(`❌ Error processing subreddit r/${subreddit}:`, error);

        // Update task status to failed
        await updateTaskStatus(supabaseClient, taskId, 'failed', {
          error_message: error.message
        });

        return {
          taskId,
          subreddit,
          ideasGenerated: 0,
          postsAnalyzed: 0,
          postsSkipped: 0,
          postsProcessed: 0,
          success: false,
          error: error.message
        };
      }
    });

    // Wait for all subreddit analyses to complete
    const results = await Promise.all(subredditTasks);

    // Aggregate results
    for (const result of results) {
      totalIdeasGenerated += result.ideasGenerated;
      totalPostsProcessed += result.postsProcessed;

      taskResults.push({
        taskId: result.taskId,
        subreddit: result.subreddit,
        ideasGenerated: result.ideasGenerated,
        postsAnalyzed: result.postsAnalyzed,
        postsSkipped: result.postsSkipped,
        success: result.success,
        error: result.error
      });

      console.log(`\n📋 SUBREDDIT r/${result.subreddit} FINAL STATS:`);
      console.log(`   💡 Ideas Generated & Saved: ${result.ideasGenerated}`);
      console.log(`   📝 Posts Analyzed: ${result.postsAnalyzed}`);
      console.log(`   ⏭️  Posts Skipped: ${result.postsSkipped}`);
      console.log(`   📊 Total Posts: ${result.postsProcessed}`);
      console.log(`   ✅ Status: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      if (result.error) {
        console.log(`   ❌ Error: ${result.error}`);
      }
    }

    // 合并所有批次统计到一个日志
    const successCount = results.filter(r => r.success).length;
    const successRate = ((successCount / results.length) * 100).toFixed(1);
    const avgIdeasPerSubreddit = (totalIdeasGenerated / Math.max(subreddits.length, 1)).toFixed(1);

    console.log(`\n🎉 Batch Complete (${target_date}): ${totalIdeasGenerated} ideas saved from ${totalPostsProcessed} posts across ${subreddits.length} subreddits (${successCount}/${results.length} success ${successRate}%, ${avgIdeasPerSubreddit} ideas/subreddit)`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully generated ${totalIdeasGenerated} ideas for ${subreddits.length} subreddits with ${totalPostsProcessed} posts processed`,
        totalIdeasGenerated,
        subredditsProcessed: subreddits.length,
        postsProcessed: totalPostsProcessed,
        taskResults
      } as AnalyzerResponse),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('❌ Enhanced Gemini Balance analysis failed:', error);
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Enhanced Gemini Balance analysis failed',
        error: error.message,
        totalIdeasGenerated: 0,
        subredditsProcessed: 0,
        postsProcessed: 0,
        taskResults: []
      } as AnalyzerResponse),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
})