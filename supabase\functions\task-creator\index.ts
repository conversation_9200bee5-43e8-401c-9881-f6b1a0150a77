
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface TaskCreatorRequest {
  targetDate: string;           // YYYY-MM-DD format
  industryIds?: number[];       // 可选，默认处理所有行业
  forceCreate?: boolean;        // 是否强制创建（即使有pending任务）
}

interface TaskCreatorResponse {
  success: boolean;
  batchId: string;
  tasksCreated: number;
  message: string;
  errors?: string[];
}

// Industry mapping for subreddit-to-industry assignment
const INDUSTRY_MAPPING = {
  'SaaS & Cloud Services': {
    id: 1,
    subreddits: ['SaaS', 'cloud', 'aws', 'azure', 'googlecloud', 'SaaSMarketing', 'microsaas', 'SaaSSales']
  },


  'Mobile App Development': {
    id: 4,
    subreddits: ['androiddev', 'iOSProgramming', 'flutter', 'UIUX', 'FlutterDev', 'reactnative', 'ionic']
  },
  'Web & Frontend Development': {
    id: 5,
    subreddits: ['webdev', 'javascript', 'reactjs', 'webassembly', 'Frontend', 'web_design', 'learnjavascript']
  },
  'No-Code/Low-Code Platforms': {
    id: 6,
    subreddits: ['NoCode', 'LowCode', 'Bubble', 'Makerpad', 'nocode', 'Airtable', 'zapier', 'webflow', 'NoCodeSaaS', 'lowcode']
  },

  'AI & Machine Learning': {
    id: 8,
    subreddits: ['MachineLearning', 'datascience', 'OpenAI', 'LLM', 'LanguageTechnology', 'DeepLearning', 'NeuralNetworks', 'ArtificialIntelligence', 'AI', 'ChatGPT', 'ArtificialInteligence', 'artificial', 'aiArt', 'CharGPTPromptGenius', 'LocalLLaMA', 'ChatGPTPro', 'ChatGPTCoding', 'ClaudeAI', 'aivideo', 'AI_Agents', 'automation', 'aipromptprogramming', 'Bard', 'perplexity_ai', 'GeminiAI', 'cursor', 'GoogleGeminiAI', 'DeepSeek', 'midjourney', 'StableDiffusion', 'PromptEngineering', 'ollama', 'LocalLLM', 'OpenSourceAi']
  },
  'E-commerce & Retail': {
    id: 9,
    subreddits: ['ecommerce', 'Shopify', 'AmazonSeller', 'AmazonFBA', 'dropship', 'Etsy', 'EtsySellers', 'reviewmyshopify', 'ecommercemarketing']
  },
  'Health & Fitness Tech': {
    id: 10,
    subreddits: ['fitness', 'DigitalHealth', 'WearOS', 'healthtech', 'MedTech', 'QuantifiedSelf', 'sleephackers', 'Biohackers', 'healthIT', 'Fitness', 'strength_training', 'loseit', 'Health', 'GYM', 'GymMotivation', 'workout', 'fitness30plus', 'physicaltherapy', 'personaltraining', 'WeightTraining']
  },
  'EdTech': {
    id: 11,
    subreddits: ['edtech', 'learnprogramming', 'OnlineTutoring', 'education', 'instructionaldesign', 'Elearning', 'teachers']
  },
  'FinTech': {
    id: 12,
    subreddits: ['fintech', 'InsurTech']
  },
  'Startup & Business': {
    id: 13,
    subreddits: ['Entrepreneur', 'business', 'startups', 'EntrepreneurRideAlong', 'SideProject', 'startup', 'ycombinator', 'Entrepreneurship', 'indiehackers', 'Entrepreneurs', 'growmybusiness', 'indiebiz', 'thesidehustle']
  },
  'Consumer Services & Freelance': {
    id: 14,
    subreddits: ['SideHustle', 'smallbusiness', 'freelance', 'BeerMoney', 'DigitalNomad', 'Fiverr', 'WorkOnline', 'forhire', 'freelanceWriters', 'freelance_forhire', 'Upwork', 'Freelancers', 'remotework', 'RemoteJobHunters']
  },
  'Enterprise & B2B Services': {
    id: 15,
    subreddits: ['b2b', 'CRM', 'Procurement']
  },
  'Digital Marketing & SEO': {
    id: 16,
    subreddits: ['SEO', 'DigitalMarketing', 'digital_marketing', 'Affiliatemarketing', 'PPC', 'advertising', 'FacebookAds', 'content_marketing', 'bigseo', 'Emailmarketing', 'AskMarketing', 'googleads', 'MarketingResearch', 'GrowthHacking', 'DigitalMarketingHack', 'TechSEO', 'seogrowth', 'marketing']
  },
  'Social Media Marketing & Influencers': {
    id: 17,
    subreddits: ['socialmedia', 'discord', 'communitymanagement', 'SocialMediaMarketing', 'BeautyFuruChatter', 'Instagram', 'InstagramMarketing', 'inflencermarketing', 'InstagramGrowthTips']
  },
  'Media & Content Creation': {
    id: 18,
    subreddits: ['youtubers', 'podcasting', 'CreatorEconomy', 'vlogging', 'NewTubers', 'ContentCreators', 'blogging', 'VideoEditing', 'bideography', 'premiere', 'editors', 'finalcutpro', 'BideoEditors', 'Youtubevideo']
  },
  'Photography & Visual Arts': {
    id: 19,
    subreddits: ['photography', 'analog', 'AskPhotography', 'streetphotography', 'postprocessing', 'AnalogCommunity', 'WeddingPhotography', 'Beginning_Photography']
  },
  'Design & Creative Tools': {
    id: 20,
    subreddits: ['design', 'graphic_design', 'web_design', 'UI_Design', 'Adobe', 'Figma', 'creativity', 'typography', 'logodesign', 'UXDesign', 'userexperience', 'UXResearch', 'learndesign', 'product_design', 'UX_Design', 'FigmaDesign']
  },
  'Travel & Transportation': {
    id: 21,
    subreddits: ['travel', 'solotravel', 'airbnb', 'wanderlust', 'shoestring', 'travelhacks', 'backpacking', 'DigitalNomad']
  },
  'GreenTech & Sustainability': {
    id: 22,
    subreddits: ['sustainability', 'renewable', 'cleantech', 'RenewableEnergy', 'Envirotech', 'solar']
  },
  'Logistics & Supply Chain': {
    id: 23,
    subreddits: ['logistics', 'warehouse', 'operations', 'supplychain', 'inventory']
  },
  'Gaming & Entertainment': {
    id: 24,
    subreddits: ['gaming', 'gamedev', 'VirtualReality', 'GamingIndustry', 'eSports', 'VRGaming', 'boardgames']
  },

  'AR/VR & Metaverse': {
    id: 26,
    subreddits: ['virtualreality', 'oculus', 'augmentedreality', 'Metaverse']
  },
  'BioTech & MedTech': {
    id: 27,
    subreddits: ['biotech', 'biotechnology', 'bioinformatics', 'genomics', 'labrats']
  },
  'LegalTech': {
    id: 28,
    subreddits: ['legaltech', 'law', 'legaladvice']
  },
  'PropTech': {
    id: 29,
    subreddits: ['PropTech', 'RealEstate', 'SmartHome']
  },
  'Data Science & Analytics': {
    id: 30,
    subreddits: ['datascience', 'analytics', 'statistics', 'tableau', 'PowerBI', 'bigdata', 'dataengineering', 'BusinessIntelligence', 'dataanalysis', 'dataanalytics']
  },
  'Blockchain & Cryptocurrency': {
    id: 31,
    subreddits: ['CryptoCurrency', 'blockchain', 'ethereum', 'Bitcoin', 'DeFi', 'NFT', 'Web3', 'ethtrader', 'CryptoMarkets', 'solana', 'BitcoinBeginners', 'defi', 'web3']
  },
  'Stock Investment & Trading': {
    id: 32,
    subreddits: ['stocks', 'Daytrading', 'SotckMarket', 'investing', 'finance', 'options', 'dividends', 'ValueInvesting', 'Trading', 'swingtrading', 'StocksAndTrading']
  },
  'Financial Independence & Personal Finance': {
    id: 33,
    subreddits: ['TheRaceTo10Million', 'Fire', 'fatFIRE', 'leanfire', 'personalfinance', 'Frugal', 'financialindependence', 'FinancialPlanning', 'UKPersonalFinance', 'PersonalFinanceCanada', 'lifehacks', 'productivity', 'getdisciplined', 'lifehack']
  },
  'Audio & Podcast': {
    id: 34,
    subreddits: ['podcasting', 'podcasts', 'audio', 'spotify', 'audioengineering', 'voiceover', 'audiobooks']
  },
  'AgTech': {
    id: 35,
    subreddits: ['agriculture', 'farming', 'AgTech', 'permaculture', 'gardening']
  },
  'Pet Care & Community': {
    id: 36,
    subreddits: ['cats', 'dogs', 'Dogtraining', 'Aquariums', 'dogswithjobs', 'RATS', 'BeardedDragons', 'birding', 'DOG', 'DogAdvice', 'cat', 'Ornithology', 'Pets', 'germanshepherds', 'reptiles', 'herpetology', 'ballpython', 'leopardgeckos', 'turtle', 'PetAdvice', 'DogTrainingTips', 'Dogowners', 'dogbreed', 'DogBreeds101', 'cockatiel', 'doggrooming', 'CatAdvice', 'puppy101']
  },
  'Family & Parenting': {
    id: 37,
    subreddits: ['Parenting', 'daddit', 'SingleParents', 'beyondthebump', 'toddlers', 'NewParents', 'raisingkids', 'parentsofmultiples', 'Parents']
  },
  'General/Trending Topics': {
    id: 38,
    subreddits: [
      'AskReddit', 'IAMA', 'funny', 'worldnews', 'todayilearned',
      'aww', 'Music', 'movies', 'memes', 'Showerthoughts', 'science',
      'pics', 'Jokes', 'news', 'explainlikeimfive', 'books', 'food',
      'LifeProTips', 'DIY', 'GetMotivated', 'askscience'
    ]
  }
}

// Helper function to get all subreddit-industry mappings
function getAllSubredditMappings(targetIndustries: number[]): Array<{subreddit: string, industryId: number}> {
  const mappings: Array<{subreddit: string, industryId: number}> = [];

  for (const [industryName, config] of Object.entries(INDUSTRY_MAPPING)) {
    if (targetIndustries.includes(config.id)) {
      config.subreddits.forEach(subreddit => {
        if (subreddit && typeof subreddit === 'string' && subreddit.trim()) {
          // Normalize subreddit name to lowercase to prevent duplicates
          const normalizedSubreddit = subreddit.trim().toLowerCase();
          mappings.push({
            subreddit: normalizedSubreddit,
            industryId: config.id
          });
        } else {
          console.warn(`Invalid subreddit found in industry ${config.id}: ${subreddit}`);
        }
      });
    }
  }

  return mappings;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { targetDate, industryIds, forceCreate = false }: TaskCreatorRequest = await req.json()

    // Validate targetDate format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(targetDate)) {
      throw new Error('Invalid date format. Must be YYYY-MM-DD')
    }

    // Validate that the date is not in the future
    const targetDateObj = new Date(targetDate)
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today
    
    if (targetDateObj > today) {
      throw new Error('Cannot scrape data from future dates')
    }

    // Validate that the date is not too old (optional, you can adjust this)
    const minDate = new Date('2020-01-01')
    if (targetDateObj < minDate) {
      throw new Error('Cannot scrape data from before 2020-01-01')
    }

    // Generate unique batch ID
    const batchId = crypto.randomUUID()

    // Get target industries
    let targetIndustries: number[]
    if (industryIds && industryIds.length > 0) {
      targetIndustries = industryIds
    } else {
      // Get all industries
      const { data: industries, error: industriesError } = await supabaseClient
        .from('industries')
        .select('id')
      
      if (industriesError) {
        throw new Error(`Failed to fetch industries: ${industriesError.message}`)
      }
      
      targetIndustries = industries.map(ind => ind.id)
    }

    // Get all subreddit mappings for target industries
    const subredditMappings = getAllSubredditMappings(targetIndustries);
    console.log(`Found ${subredditMappings.length} subreddits across ${targetIndustries.length} industries`);
    console.log(`Target industries: ${targetIndustries.join(', ')}`);
    console.log(`Sample mappings:`, subredditMappings.slice(0, 5));

    // Check for existing pending tasks if not forcing creation (now at subreddit level)
    if (!forceCreate) {
      const subredditsToCheck = subredditMappings.map(m => m.subreddit);
      const { data: pendingTasks, error: pendingError } = await supabaseClient
        .from('scrape_tasks')
        .select('subreddit, industry_id')
        .in('status', ['pending_scrape', 'scraping', 'pending_analysis', 'analyzing'])
        .in('subreddit', subredditsToCheck)
        .eq('target_date', targetDate)

      if (pendingError) {
        console.warn(`Warning: Could not check pending tasks: ${pendingError.message}`)
      } else if (pendingTasks && pendingTasks.length > 0) {
        const pendingSubreddits = pendingTasks.map(task => task.subreddit)
        return new Response(
          JSON.stringify({
            success: false,
            batchId: '',
            tasksCreated: 0,
            message: `There are already pending tasks for date ${targetDate} and subreddits: ${pendingSubreddits.join(', ')}. Use forceCreate=true to override.`,
            errors: [`Pending tasks exist for ${pendingTasks.length} subreddits on ${targetDate}`]
          } as TaskCreatorResponse),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 409
          }
        )
      }
    }

    // Create scrape tasks for each subreddit
    const tasksToCreate = subredditMappings.map(mapping => ({
      industry_id: mapping.industryId,
      subreddit: mapping.subreddit,
      target_date: targetDate,
      status: 'pending_scrape',
      batch_id: batchId,
      posts_scraped: 0,
      posts_processed: 0,
      ideas_generated: 0,
      retry_count: 0,
      max_retries: 3,
      created_at: new Date().toISOString()
    }))

    const { data: createdTasks, error: createError } = await supabaseClient
      .from('scrape_tasks')
      .insert(tasksToCreate)
      .select()

    if (createError) {
      throw new Error(`Failed to create tasks: ${createError.message}`)
    }

    console.log(`Created ${createdTasks.length} subreddit-level scrape tasks with batch ID: ${batchId} for date: ${targetDate}`)
    console.log(`Tasks created for ${targetIndustries.length} industries covering ${subredditMappings.length} subreddits`)

    return new Response(
      JSON.stringify({
        success: true,
        batchId,
        tasksCreated: createdTasks.length,
        message: `Successfully created ${createdTasks.length} subreddit-level scrape tasks for date: ${targetDate} (${targetIndustries.length} industries, ${subredditMappings.length} subreddits)`,
        errors: []
      } as TaskCreatorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Task Creator Error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        batchId: '',
        tasksCreated: 0,
        message: 'Failed to create scrape tasks',
        errors: [error.message]
      } as TaskCreatorResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
}) 